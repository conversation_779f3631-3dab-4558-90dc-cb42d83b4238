## imkit (@rongcloud/imkit)

IMkit 基于 IMLib 通用化的 UI 封装，定位于通用的聊天会话场景（单聊/群聊），帮助开发者快速搭建一个功能集相对完整的即时 IM 通讯产品，提高接入效率。

## 安装依赖
`npm install`
## 开发调试
`npm run dev`

## 编译打包
`npm run build`

---

## 功能支持说明

1. 会话功能列表

   1. 置顶 ✅
   2. 取消置顶 ✅
   3. 消息免打扰 ✅
   4. 取消打扰 ✅
   5. 删除 ✅
   6. 自定义会话右键 ✅

2. 消息功能列表

   1. 复制 ✅
   2. 引用 ✅
   3. 撤回 ✅
   4. 删除 ✅
   5. 转发 ✅
   6. 已读/未读【单聊】 ✅ 

3. 发送功能列表
   1. 文字 ✅
   2. 图片 ✅
   3. 文件 ✅
   4. @消息 ✅
   5. 自定义消息 ✅

4. 界面 UI 基础配置选项支持

   1. 背景色 ❌
   2. 边框色 ❌
   3. 语言切换 ✅

---
## 项目介绍

IMKit 项目使用 [stencijs](https://stenciljs.com/) + TypeScript 实现组件开发。

项目目前支持 `会话列表组件`、`消息列表组件`、`输入框组件` 三类。

### 目录结构

``` shell
├─ build                      # 打包配置文件
├─ src                        
│  ├─ assets                  # 静态资源文件   
│  ├─ components              # 组件     
│  │  ├─ conversation-list    # 会话列表组件                  
│  │  ├─ message-editor       # 输入框组件              
│  │  └─ message-list         # 消息列表组件            
│  ├─ core                    # 核心处理模块，包含消息处理，会话处理，本地存储处理等          
│  ├─ index.ts                #     
│  ├─ language                # 语言资源文件    
│  └─ utils                   # 工具目录 
├─ README.md                  # readme  
├─ stencil.config.ts          # stencil 配置文件                      
├─ package.json               # 包依赖
└─ tsconfig.json              # ts 配置文件

```

### 源码引入方式

1. 执行 `npm run build` 得到 dist 目录。把 dist 目录拷贝到项目中，建议更改 dist 文件夹名称。
2. 在 package.json 中添加 `"@rongcloud/imkit": "file:XXX"` 依赖。XXX 为 build 生成的 dist 文件夹拷贝到您项目中的目录。

## IMKit 集成使用

IMKit 使用文档地址：[https://doc.rongcloud.cn/im/Web/5.X/ui/quick_integration](https://doc.rongcloud.cn/im/Web/5.X/ui/quick_integration)